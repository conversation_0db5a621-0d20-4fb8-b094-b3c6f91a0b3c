document.addEventListener("DOMContentLoaded", () => {
    gsap.registerPlugin(ScrollTrigger, SplitText);


    const lenis = new Lenis();
    lenis.on("scroll", ScrollTrigger.update);
    gsap.ticker.add((time) => {
        lenis.raf(time * 1000);
    });

    gsap.ticker.lagSmoothing(0);

    const header1Split = new SplitText(".header-1", {
        type: "chars",
        charsClass: "char",
    });
    const titleSplit = new SplitText(".tooltip .title h2", {
        type: "lines",
        charsClass: "line",
    });

    const descriptionSplit = new SplitText(".tooltip .description p", {
        type: "lines",
        charsClass: "line",
    });

    header1Split.chars.forEach(
        (char) => (char.innerHTML = `<span>${char.innerHTML}</span>`)
    );
    [...titleSplit.lines, ...descriptionSplit.lines].forEach(
        (line) => (line.innerHTML = `<span>${line.innerHTML}</span>`)
    );

    const animOptions = { duration: 1, ease: "power3.out", stagger: 0.025 };

    const tooltipSelectors = [
        {
            trigger: 0.65,
            element: [
                ".tooltip:nth-child(1) .icon img",
                ".tooltip:nth-child(1) .title .line > span",
                ".tooltip:nth-child(1) .description .line > span",
            ]
        },

        {
            trigger: 0.85,
            element: [
                ".tooltip:nth-child(2) .icon img",
                ".tooltip:nth-child(2) .title .line > span",
                ".tooltip:nth-child(2) .description .line > span",
            ]
        },
    ];

    ScrollTrigger.create({
        trigger: ".product-overview",
        start: "75% bottom",
        onEnter: () =>
            gsap.to(".header-1 .char > span", {
                y: "0%",
                duration: 1,
                ease: "power3.out",
                stagger: 0.025,
            }),
        onLeaveBack: () =>
            gsap.to(".header-1 h1 .char > span", {
                y: "100%",
                duration: 1,
                ease: "power3.out",
                stagger: 0.025,
            }),
    });


    let model,
        currentRotaion = 0,
        modelSize;

    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(
        60,
        window.innerWidth / window.innerHeight,
        0.1,
        1000
    );

    const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });

    renderer.setClearColor(0x000000, 0);
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    renderer.outputEncoding = THREE.LinearEncoding;
    renderer.toneMapping = THREE.NoToneMapping;
    renderer.toneMappingExposure = 1;
    document.querySelector(".modal-container").appendChild(renderer.domElement);

    scene.add(new THREE.AmbientLight(0xffffff, 0.7));

    const mainLight = new THREE.DirectionalLight(0xffffff, 1);
    mainLight.position.set(1, 2, 3);
    mainLight.castShadow = true;
    mainLight.shadow.bias = -0.001;
    mainLight.shadow.mapSize.width = 1024;
    mainLight.shadow.mapSize.height = 1024;
    scene.add(mainLight);

    const fillLight = new THREE.DirectionalLight(0xffffff, 0.5);
    fillLight.position.set(-2, 0, -2);
    scene.add(fillLight);

    function setupModel() {
        if (!model || !modelSize) return;

        const isMobile = window.innerWidth < 1000;
        const box = new THREE.Box3().setFromObject(model);
        const center = box.getCenter(new THREE.Vector3());

        model.position.set(
            isMobile ? center.x + modelSize.x * 0.1 : center.x - modelSize.x * 0.1,
            center.y,
            center.z
        );


        model.rotation.z = isMobile ? 0 : THREE.MathUtils.degToRad(-25);


        const cameraDistance = isMobile ? 2 : 1.25;
        camera.position.set(
            0,
            0,
            Math.max(modelSize.x, modelSize.y, modelSize.z) * cameraDistance
        );
        camera.lookAt(0, 0, 0);
    };


    new GLTFLoader().load("shaker.glb", (gltf) => {
        model = gltf.scene;


        model.traverse((child) => {
            if (node.isMesh && node.material) {
                Object.assign(node.material, {
                    metalness: 0.05,
                    roughness: 0.9,

                });
            }
        });
    });

    const box = new THREE.Box3().setFromObject(model);
    const size = box.getSize(new THREE.Vector3());
    modelSize = size;

    scene.add(model);
    setupModel();



    function animate() {
        requestAnimationFrame(animate);
        renderer.render(scene, camera);
    }

    animate();


    window.addEventListener("resize", () => {
        camera.aspect = window.innerWidth / window.innerHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(window.innerWidth, window.innerHeight);
        setupModel();
    });


    ScrollTrigger.create({
        trigger: ".product-overview",
        start: "top top",
        end: '+=$(window.innerHeight * 10)px',
        pin: true,
        pinSpacing: false,
        scrub: 1,
        markers: true,
        onUpdate: ({ progress }) => {
            const headerProgress = Math.max(0, Math.min(1, (progress - 0.05) / 0.3));
            gsap.to(".header-1", {
                xPercent:
                    progress < 0.05 ? 0 : Progress > 0.35 ? -100 : -100 * headerProgress,
            });

            const maskSize =
                progress < 0.2
                    ? 0
                    : progress > 0.3
                        ? 100
                        : 100 * ((progress - 0.2) / 0.1);

            gsap.to(".circular-mask", {
                clipPath: `circle(${maskSize}% at 50% 50%)`,
            });


            const header2Progress = (progress - 0.15) / .35;
            const header2XPercent =
                progress < .15
                    ? 100
                    : progress > 0.5
                        ? 0
                        : 100 - 300 * header2Progress;

            gsap.to(".header-2", {
                xPercent: header2XPercent,
            });

            const scaleX =
                progress < .45
                    ? 0
                    : progress > .65
                        ? 100
                        : 100 * ((progress - 0.45) / 0.2);

            gsap.to(".tooltip .divider", {
                scaleX: '${scaleX}%',
                ...animOptions

            });


            tooltipSelectors.forEach(({ trigger, element }) => {
                gsap.to(element, {
                    y: progress >= trigger ? "0%" : "125%",
                    ...animOptions,
                });

            });


            if (model && progress >= 0.05) {
                const rotationProgress = (progress - 0.05) / 0.95;
                const rotationDiff = targetRotation - currentRotation;
                if (Math.abs(rotationDiff) > 0.001) {
                    model.rotateOnAxis(new THREE.Vector3(0, 1, 0), rotationDiff);
                    currentRotation = targetRotation;

                }
            }

        },

    });

});

