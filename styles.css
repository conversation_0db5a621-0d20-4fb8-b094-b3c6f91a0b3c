* {
    margin: 0;
    padding: 0;
    font-family: 'Poppins', sans-serif;
    box-sizing: border-box;
}


h1 {
    font-size: 4rem;
    font-weight: 500;
    line-height: 1;
}

h2 {
    font-size: 3rem;
    font-weight: 500;
    line-height: 1.125;
    letter-spacing: -0.03rem;

}


p {
    font-size: 1.1rem;
    line-height: 1.5;
    font-weight: 500;
}


section {
    position: relative;
    width: 100vw;
    height: 100svh;
    background-color: #e0dfdf;
    color: #0d0d0d;
    overflow: hidden;
}

.intro,
.outro {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #0d0d0d;
    color: #e0dfdf;
    padding: 2rem;
}

.model-contaoner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    z-index: 100;
}


.header-1 {
    position: absolute;
    width: 200vw;
    height: 100svh;
    transform: translateX(0%);
}


.header-2 {
    position: fixed;
    left: 0;
    top: 0;
    width: 150vw;
    height: 100svh;
    transform: translateX(100%);
    color: #e0dfdf;
    transform: translateX(100%);
    z-index: 2;
}

.header-1,
.header-2 {
    display: flex;
    align-items: center;
    padding: 0 2rem;
}


.header-1 h1,
.header-2 h1 {
    width: 100%;
    font-size: 15vw;
    line-height: 1;
    letter-spacing: -0.02em;
}

.circular-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #0d0d0d;
    clip-path: circle(0% at 50% 50%);
}

.tooltips {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 75%;
    height: 75%;
    display: flex;
    gap: 15rem
}


.tooltip {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: .5rem;
    color: #e0dfdf;
}


.tooltip:nth-child(2) {
    justify-content: flex-end;
    align-items: flex-end;
}

.tooltip .divider {
    position: relative;
    width: 100%;
    height: 1px;
    background-color: #5f5f5f;
    margin: .5rem 0;
    transform: scaleX(0);
}


.tooltip:nth-child(1) .divider {
    transform-origin: right center
}

.tooltip:nth-child(2) .divider {
    transform-origin: left center
}


.tooltip .icon {
    width: 4rem;
    overflow: hidden;
}

.tooltip .icon img {
    width: 100%;
    height: auto;
}


.tooltip .description {
    color: #5f5f5f;
}

.tooltip:nth-child(2) .icon,
.tooltip:nth-child(2) .title,
.tooltip .description {
    width: 75%;
}

.header-1 h1 .char,
.tooltip .title .line,
.tooltip .description .line {
    display: inline-block;
    overflow: hidden;
}

.header-1 h1 .char>span,
.tooltip .icon img,
.tooltip .title .line>span,
.tooltip .description .line>span {
    display: block;
    position: relative;
    transform: translateY(100%);
    will-change: transform;
}

@media (max-width: 768px) {

    h1{
        text-align: center;
        font-size: 2rem;
    }

    h2 {
        font-size: 1.5rem;
    }

    .tooltips {
        width: 100%;
        align-items: center;
        flex-direction: column;
        gap: 2rem;
    }

    .tooltip {
        width: 85%;
    }

    .tooltip:nth-child(2) {
        justify-content: flex-start;
        align-items: flex-start;
    }

    .tooltip .divider {
        width: 70%;
    }


    .tooltip:nth-child(2) .divider {
        transform-origin: right center;
    }


    .tooltip:nth-child(2) .icon,
    .tooltip:nth-child(2) .title {
        width: 100%;
    }

}